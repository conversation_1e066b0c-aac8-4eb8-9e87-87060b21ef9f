package com.arrayberry.nowreceipt

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for receipt processing, focusing on amount detection improvements.
 */
class ExampleUnitTest {

    private val textProcessor = TextProcessor()

    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun testAmountDetection_PrefersTotalOverSubtotal() {
        // Simulate a receipt with subtotal, tax, and total
        val receiptText = """
            WALMART STORE
            123 MAIN ST
            ITEM 1          $15.99
            ITEM 2          $8.50
            ITEM 3          $12.75
            SUBTOTAL        $37.24
            TAX             $2.98
            TOTAL           $40.22
            THANK YOU
        """.trimIndent()

        val lines = textProcessor.preprocessText(receiptText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        // The top candidate should be the total ($40.22), not the subtotal ($37.24)
        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // The top candidate should be the total amount
        assertEquals("Top candidate should be the total amount", 40.22, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testAmountDetection_HandlesVariousFormats() {
        val receiptText = """
            RESTAURANT ABC
            SUB-TOTAL       45.67
            SALES TAX       3.65
            GRAND TOTAL     $49.32
        """.trimIndent()

        val lines = textProcessor.preprocessText(receiptText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // Should pick the grand total
        assertEquals("Should pick grand total", 49.32, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testAmountDetection_IgnoresItemPrices() {
        val receiptText = """
            GROCERY STORE
            MILK            $4.99
            BREAD           $2.50
            EGGS            $3.25
            SUBTOTAL        $10.74
            TAX             $0.86
            TOTAL           $11.60
        """.trimIndent()

        val lines = textProcessor.preprocessText(receiptText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // Should pick the total, not individual item prices
        assertEquals("Should pick total over item prices", 11.60, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testTextProcessing_HandlesFragmentedLines() {
        // Test that the text processor can handle fragmented lines
        // This simulates what happens when OCR breaks a line like "SUBTOTAL $45.67"
        // into separate fragments that appear far apart in the raw text
        val fragmentedText = """
            WALMART
            STORE
            ITEM 1
            15.99
            ITEM 2
            8.50
            SUBTOTAL
            37.24
            TAX
            2.98
            TOTAL
            40.22
        """.trimIndent()

        val lines = textProcessor.preprocessText(fragmentedText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        // Even with fragmented text, should still prefer total over subtotal
        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // Should still pick the total amount despite fragmentation
        assertEquals("Should pick total even with fragmented text", 40.22, topAmount ?: 0.0, 0.01)
    }
}