package com.arrayberry.nowreceipt

import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for receipt processing, focusing on amount detection improvements.
 */
class ExampleUnitTest {

    private val textProcessor = TextProcessor()

    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun testAmountDetection_PrefersTotalOverSubtotal() {
        // Simulate a receipt with subtotal, tax, and total
        val receiptText = """
            WALMART STORE
            123 MAIN ST
            ITEM 1          $15.99
            ITEM 2          $8.50
            ITEM 3          $12.75
            SUBTOTAL        $37.24
            TAX             $2.98
            TOTAL           $40.22
            THANK YOU
        """.trimIndent()

        val lines = textProcessor.preprocessText(receiptText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        // The top candidate should be the total ($40.22), not the subtotal ($37.24)
        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // The top candidate should be the total amount
        assertEquals("Top candidate should be the total amount", 40.22, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testAmountDetection_HandlesVariousFormats() {
        val receiptText = """
            RESTAURANT ABC
            SUB-TOTAL       45.67
            SALES TAX       3.65
            GRAND TOTAL     $49.32
        """.trimIndent()

        val lines = textProcessor.preprocessText(receiptText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // Should pick the grand total
        assertEquals("Should pick grand total", 49.32, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testAmountDetection_IgnoresItemPrices() {
        val receiptText = """
            GROCERY STORE
            MILK            $4.99
            BREAD           $2.50
            EGGS            $3.25
            SUBTOTAL        $10.74
            TAX             $0.86
            TOTAL           $11.60
        """.trimIndent()

        val lines = textProcessor.preprocessText(receiptText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // Should pick the total, not individual item prices
        assertEquals("Should pick total over item prices", 11.60, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testTextProcessing_HandlesFragmentedLines() {
        // Test that the text processor can handle fragmented lines
        // This simulates what happens when OCR breaks a line like "SUBTOTAL $45.67"
        // into separate fragments that appear far apart in the raw text
        val fragmentedText = """
            WALMART
            STORE
            ITEM 1
            15.99
            ITEM 2
            8.50
            SUBTOTAL
            37.24
            TAX
            2.98
            TOTAL
            40.22
        """.trimIndent()

        val lines = textProcessor.preprocessText(fragmentedText)
        val candidates = textProcessor.extractAmountCandidates(lines)

        assertTrue("Should have amount candidates", candidates.isNotEmpty())

        // Even with fragmented text, should still prefer total over subtotal
        val topCandidate = candidates.first()
        val topAmount = topCandidate.text.replace("$", "").replace(",", "").toDoubleOrNull()

        // Should still pick the total amount despite fragmentation
        assertEquals("Should pick total even with fragmented text", 40.22, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testDollaramaReceipt_CorrectMerchantAndAmount() {
        // Test based on the actual Dollarama receipt image
        val dollaramaReceiptText = """
            DOLLARAMA
            11700 Yonge Street Unit 2
            Richmond Hill ON L4E 0K4
            HST *********
            FOAM BOARD         667888505333  4.00 H
            PINS               667888414277  1.50 H
            PINS               667888297535  4.00 H
            BALLOONS           667888464968  4.25 H
            BALLOON KIT        667888594245  2.00 H
            SUBTOTAL                         $15.75
            HST 13%                          $2.05
            TOTAL                            $17.80
            AMEX                             $17.80
            TYPE: PURCHASE
            ACCT: AMERICAN EXPRESS
            AMOUNT:              $17.80
            CARD NUMBER:         xxxxxxxxxx4424
            DATE/TIME:           25/06/05 19:30:51
        """.trimIndent()

        val lines = textProcessor.preprocessText(dollaramaReceiptText)

        // Test merchant detection
        val merchantCandidates = textProcessor.extractMerchantCandidates(lines)
        assertTrue("Should detect merchant", merchantCandidates.isNotEmpty())
        val topMerchant = merchantCandidates.first().text
        assertEquals("Should detect DOLLARAMA as merchant", "DOLLARAMA", topMerchant)

        // Test amount detection
        val amountCandidates = textProcessor.extractAmountCandidates(lines)
        assertTrue("Should detect amounts", amountCandidates.isNotEmpty())
        val topAmount = amountCandidates.first().text.replace("$", "").replace(",", "").toDoubleOrNull()
        assertEquals("Should detect $17.80 as the total amount", 17.80, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testFragmentedTextMerging() {
        // Test the line merging functionality for fragmented OCR output
        val fragmentedText = """
            DOLL
            ARAMA
            SUBTOTAL
            15.75
            TOTAL
            17.80
        """.trimIndent()

        val lines = textProcessor.preprocessText(fragmentedText)

        // Should merge fragmented words and labels with amounts
        assertTrue("Should have fewer lines after merging", lines.size < 6)

        // Should contain merged merchant name
        assertTrue("Should contain DOLLARAMA", lines.any { it.contains("DOLLARAMA") })

        // Should contain merged amount lines
        assertTrue("Should contain SUBTOTAL with amount", lines.any { it.contains("SUBTOTAL") && it.contains("15.75") })
        assertTrue("Should contain TOTAL with amount", lines.any { it.contains("TOTAL") && it.contains("17.80") })
    }

    @Test
    fun testHeavilyFragmentedDollaramaText() {
        // Test with the actual fragmented text from the Dollarama receipt
        val fragmentedText = """
            7964
            Com
            H
            H
            $2.05
            4.00 H
            $17.80
            $17.80
            $15.75
            2.00
            arama.
            DOLLARAMA
            SUBTOTAL
            HST
            13%
            TOTAL
            17.80
        """.trimIndent()

        val lines = textProcessor.preprocessText(fragmentedText)

        // Test merchant detection with reconstructed text
        val merchantCandidates = textProcessor.extractMerchantCandidates(lines)
        assertTrue("Should detect merchant", merchantCandidates.isNotEmpty())
        val topMerchant = merchantCandidates.first().text
        assertEquals("Should detect DOLLARAMA as merchant", "DOLLARAMA", topMerchant)

        // Test amount detection with reconstructed text
        val amountCandidates = textProcessor.extractAmountCandidates(lines)
        assertTrue("Should detect amounts", amountCandidates.isNotEmpty())
        val topAmount = amountCandidates.first().text.replace("$", "").replace(",", "").toDoubleOrNull()
        assertEquals("Should detect $17.80 as the total amount", 17.80, topAmount ?: 0.0, 0.01)
    }

    @Test
    fun testMerchantExtractionDoesNotIncludeOtherElements() {
        // Test that merchant name extraction doesn't include subtotal or other elements
        val problematicText = """
            DOLLARAMA
            SUBTOTAL 15.75
            HST 13% 2.05
            TOTAL 17.80
        """.trimIndent()

        val lines = textProcessor.preprocessText(problematicText)
        val merchantCandidates = textProcessor.extractMerchantCandidates(lines)

        assertTrue("Should detect merchant", merchantCandidates.isNotEmpty())
        val topMerchant = merchantCandidates.first().text

        // Should be exactly "DOLLARAMA", not "DOLLARAMA SUBTOTAL 15.75"
        assertEquals("Should extract only DOLLARAMA", "DOLLARAMA", topMerchant)
        assertFalse("Should not contain SUBTOTAL", topMerchant.contains("SUBTOTAL"))
        assertFalse("Should not contain amounts", topMerchant.contains("15.75"))
    }
}