package com.arrayberry.nowreceipt

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface ReceiptDao {
    
    @Query("SELECT * FROM receipts ORDER BY createdAt DESC")
    fun getAllReceipts(): Flow<List<ReceiptEntity>>
    
    @Query("SELECT * FROM receipts WHERE id = :id")
    suspend fun getReceiptById(id: Long): ReceiptEntity?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReceipt(receipt: ReceiptEntity): Long
    
    @Update
    suspend fun updateReceipt(receipt: ReceiptEntity)
    
    @Delete
    suspend fun deleteReceipt(receipt: ReceiptEntity)
    
    @Query("DELETE FROM receipts WHERE id = :id")
    suspend fun deleteReceiptById(id: Long)
    
    @Query("SELECT * FROM receipts WHERE merchantName LIKE '%' || :searchTerm || '%' OR rawText LIKE '%' || :searchTerm || '%'")
    fun searchReceipts(searchTerm: String): Flow<List<ReceiptEntity>>
}