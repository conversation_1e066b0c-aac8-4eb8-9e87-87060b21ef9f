package com.arrayberry.nowreceipt

import android.graphics.Bitmap
import android.graphics.Rect
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import java.util.regex.Pattern
import kotlin.math.abs

class ReceiptScanner {
    
    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    private val textProcessor = TextProcessor()
    
    fun scanReceipt(bitmap: Bitmap, callback: (ReceiptData) -> Unit) {
        val image = InputImage.fromBitmap(bitmap, 0)

        textRecognizer.process(image)
            .addOnSuccessListener { visionText ->
                // Try structured text reconstruction first
                val reconstructedText = reconstructTextFromBlocks(visionText)

                // Validate reconstruction quality
                val isReconstructionGood = validateReconstruction(reconstructedText, visionText.text)

                val finalText = if (isReconstructionGood) {
                    reconstructedText
                } else {
                    // Fallback to original text if reconstruction is poor
                    visionText.text
                }

                val receiptData = extractReceiptFieldsWithML(finalText)
                callback(receiptData)
            }
            .addOnFailureListener { e ->
                callback(ReceiptData(rawText = "Error scanning receipt: ${e.message}"))
            }
    }
    
    private fun extractReceiptFieldsWithML(text: String): ReceiptData {
        val lines = textProcessor.preprocessText(text)
        
        val merchantName = extractBestMerchant(lines)
        val date = extractBestDate(lines)
        val amount = extractBestAmount(lines)
        
        return ReceiptData(
            merchantName = merchantName,
            date = date,
            amount = amount,
            rawText = text
        )
    }
    
    private fun extractBestMerchant(lines: List<String>): String? {
        val candidates = textProcessor.extractMerchantCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestDate(lines: List<String>): String? {
        val candidates = textProcessor.extractDateCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestAmount(lines: List<String>): String? {
        val candidates = textProcessor.extractAmountCandidates(lines)
        return candidates.firstOrNull()?.text
    }

    private fun reconstructTextFromBlocks(visionText: Text): String {
        // Try using ML Kit's built-in line structure first
        val mlKitLines = mutableListOf<String>()

        for (block in visionText.textBlocks) {
            for (line in block.lines) {
                // Use ML Kit's line detection, but sort elements within each line
                val sortedElements = line.elements.sortedBy { element ->
                    element.boundingBox?.centerX() ?: 0
                }
                val lineText = sortedElements.joinToString(" ") { it.text }
                if (lineText.trim().isNotEmpty()) {
                    mlKitLines.add(lineText.trim())
                }
            }
        }

        // Sort lines by their vertical position
        val linesWithPositions = mutableListOf<Pair<String, Int>>()
        for (block in visionText.textBlocks) {
            for (line in block.lines) {
                val sortedElements = line.elements.sortedBy { element ->
                    element.boundingBox?.centerX() ?: 0
                }
                val lineText = sortedElements.joinToString(" ") { it.text }
                if (lineText.trim().isNotEmpty()) {
                    val avgY = line.elements.mapNotNull { it.boundingBox?.centerY() }.average().toInt()
                    linesWithPositions.add(Pair(lineText.trim(), avgY))
                }
            }
        }

        // Sort by Y position and return
        val sortedLines = linesWithPositions.sortedBy { it.second }.map { it.first }

        // If ML Kit structure seems reasonable, use it; otherwise fall back to custom grouping
        return if (sortedLines.size > 5 && sortedLines.any { it.contains("DOLLARAMA") || it.contains("TOTAL") }) {
            sortedLines.joinToString("\n")
        } else {
            // Fallback to custom reconstruction
            reconstructTextFromElements(visionText)
        }
    }

    private fun reconstructTextFromElements(visionText: Text): String {
        // Extract all text elements with their bounding boxes
        val textElements = mutableListOf<TextElement>()

        for (block in visionText.textBlocks) {
            for (line in block.lines) {
                for (element in line.elements) {
                    element.boundingBox?.let { boundingBox ->
                        textElements.add(
                            TextElement(
                                text = element.text,
                                boundingBox = boundingBox,
                                centerY = boundingBox.centerY(),
                                centerX = boundingBox.centerX()
                            )
                        )
                    }
                }
            }
        }

        // Group elements into logical lines based on Y-coordinate proximity
        val logicalLines = groupElementsIntoLines(textElements)

        // Sort lines by Y-coordinate (top to bottom)
        val sortedLines = logicalLines.sortedBy { line ->
            line.minOfOrNull { it.centerY } ?: 0
        }

        // Convert back to text
        return sortedLines.joinToString("\n") { line ->
            // Sort elements in each line by X-coordinate (left to right)
            line.sortedBy { it.centerX }
                .joinToString(" ") { it.text }
        }
    }

    private fun groupElementsIntoLines(elements: List<TextElement>): List<List<TextElement>> {
        if (elements.isEmpty()) return emptyList()

        val lines = mutableListOf<MutableList<TextElement>>()
        val sortedElements = elements.sortedBy { it.centerY }

        for (element in sortedElements) {
            var addedToLine = false
            var bestLineIndex = -1
            var smallestYDifference = Int.MAX_VALUE

            // Find the best matching line (closest Y-coordinate)
            for (i in lines.indices) {
                val line = lines[i]
                val lineAvgY = line.map { it.centerY }.average()
                val yDifference = abs(element.centerY - lineAvgY).toInt()

                // Check if element is close enough vertically and is the best match so far
                if (yDifference <= getLineHeightThreshold(line) && yDifference < smallestYDifference) {
                    // Additional check: ensure elements don't overlap horizontally too much
                    val elementLeft = element.boundingBox.left
                    val elementRight = element.boundingBox.right
                    val hasOverlap = line.any { existing ->
                        val existingLeft = existing.boundingBox.left
                        val existingRight = existing.boundingBox.right
                        // Check for significant horizontal overlap (more than 50% of element width)
                        val overlapWidth = maxOf(0, minOf(elementRight, existingRight) - maxOf(elementLeft, existingLeft))
                        val elementWidth = elementRight - elementLeft
                        overlapWidth > elementWidth * 0.5
                    }

                    if (!hasOverlap) {
                        smallestYDifference = yDifference
                        bestLineIndex = i
                        addedToLine = true
                    }
                }
            }

            // Add to the best matching line or create new line
            if (addedToLine && bestLineIndex >= 0) {
                lines[bestLineIndex].add(element)
            } else {
                lines.add(mutableListOf(element))
            }
        }

        return lines
    }

    private fun getLineHeightThreshold(line: List<TextElement>): Int {
        if (line.isEmpty()) return 25 // Reduced default threshold

        // Calculate average height of elements in the line
        val avgHeight = line.mapNotNull { it.boundingBox.height() }.average()

        // Use 50% of average height as threshold (more conservative), with tighter bounds
        return (avgHeight * 0.5).toInt().coerceIn(12, 35)
    }

    private data class TextElement(
        val text: String,
        val boundingBox: Rect,
        val centerY: Int,
        val centerX: Int
    )

    private fun validateReconstruction(reconstructedText: String, originalText: String): Boolean {
        // Check if reconstruction preserved important content
        val reconstructedUpper = reconstructedText.uppercase()
        val originalUpper = originalText.uppercase()

        // Key indicators that should be preserved
        val keyTerms = listOf("DOLLARAMA", "TOTAL", "SUBTOTAL", "TAX", "HST", "AMOUNT")

        // Count how many key terms are preserved
        val preservedTerms = keyTerms.count { term ->
            reconstructedUpper.contains(term) && originalUpper.contains(term)
        }

        // Check if reconstruction is too fragmented (too many very short lines)
        val reconstructedLines = reconstructedText.split("\n").filter { it.trim().isNotEmpty() }
        val veryShortLines = reconstructedLines.count { it.trim().length <= 2 }
        val fragmentationRatio = if (reconstructedLines.isNotEmpty()) {
            veryShortLines.toFloat() / reconstructedLines.size
        } else 1.0f

        // Check if important amounts are preserved
        val amountPattern = Regex("\\$?\\d+\\.\\d{2}")
        val originalAmounts = amountPattern.findAll(originalText).map { it.value }.toSet()
        val reconstructedAmounts = amountPattern.findAll(reconstructedText).map { it.value }.toSet()
        val amountPreservationRatio = if (originalAmounts.isNotEmpty()) {
            reconstructedAmounts.intersect(originalAmounts).size.toFloat() / originalAmounts.size
        } else 1.0f

        // Reconstruction is good if:
        // 1. Most key terms are preserved
        // 2. Not too fragmented (less than 30% very short lines)
        // 3. Most amounts are preserved (at least 70%)
        return preservedTerms >= 2 &&
               fragmentationRatio < 0.3f &&
               amountPreservationRatio >= 0.7f
    }

}