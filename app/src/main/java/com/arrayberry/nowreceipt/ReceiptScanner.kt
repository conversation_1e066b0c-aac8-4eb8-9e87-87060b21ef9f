package com.arrayberry.nowreceipt

import android.graphics.Bitmap
import android.graphics.Rect
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.text.Text
import com.google.mlkit.vision.text.TextRecognition
import com.google.mlkit.vision.text.latin.TextRecognizerOptions
import java.util.regex.Pattern
import kotlin.math.abs

class ReceiptScanner {
    
    private val textRecognizer = TextRecognition.getClient(TextRecognizerOptions.DEFAULT_OPTIONS)
    private val textProcessor = TextProcessor()
    
    fun scanReceipt(bitmap: Bitmap, callback: (ReceiptData) -> Unit) {
        val image = InputImage.fromBitmap(bitmap, 0)

        textRecognizer.process(image)
            .addOnSuccessListener { visionText ->
                // Use structured text reconstruction instead of raw text
                val reconstructedText = reconstructTextFromBlocks(visionText)
                val receiptData = extractReceiptFieldsWithML(reconstructedText)
                callback(receiptData)
            }
            .addOnFailureListener { e ->
                callback(ReceiptData(rawText = "Error scanning receipt: ${e.message}"))
            }
    }
    
    private fun extractReceiptFieldsWithML(text: String): ReceiptData {
        val lines = textProcessor.preprocessText(text)
        
        val merchantName = extractBestMerchant(lines)
        val date = extractBestDate(lines)
        val amount = extractBestAmount(lines)
        
        return ReceiptData(
            merchantName = merchantName,
            date = date,
            amount = amount,
            rawText = text
        )
    }
    
    private fun extractBestMerchant(lines: List<String>): String? {
        val candidates = textProcessor.extractMerchantCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestDate(lines: List<String>): String? {
        val candidates = textProcessor.extractDateCandidates(lines)
        return candidates.firstOrNull()?.text
    }
    
    private fun extractBestAmount(lines: List<String>): String? {
        val candidates = textProcessor.extractAmountCandidates(lines)
        return candidates.firstOrNull()?.text
    }

    private fun reconstructTextFromBlocks(visionText: Text): String {
        // Extract all text elements with their bounding boxes
        val textElements = mutableListOf<TextElement>()

        for (block in visionText.textBlocks) {
            for (line in block.lines) {
                for (element in line.elements) {
                    element.boundingBox?.let { boundingBox ->
                        textElements.add(
                            TextElement(
                                text = element.text,
                                boundingBox = boundingBox,
                                centerY = boundingBox.centerY(),
                                centerX = boundingBox.centerX()
                            )
                        )
                    }
                }
            }
        }

        // Group elements into logical lines based on Y-coordinate proximity
        val logicalLines = groupElementsIntoLines(textElements)

        // Sort lines by Y-coordinate (top to bottom)
        val sortedLines = logicalLines.sortedBy { line ->
            line.minOfOrNull { it.centerY } ?: 0
        }

        // Convert back to text
        return sortedLines.joinToString("\n") { line ->
            // Sort elements in each line by X-coordinate (left to right)
            line.sortedBy { it.centerX }
                .joinToString(" ") { it.text }
        }
    }

    private fun groupElementsIntoLines(elements: List<TextElement>): List<List<TextElement>> {
        if (elements.isEmpty()) return emptyList()

        val lines = mutableListOf<MutableList<TextElement>>()
        val sortedElements = elements.sortedBy { it.centerY }

        for (element in sortedElements) {
            var addedToLine = false

            // Try to add to existing line if Y-coordinates are close
            for (line in lines) {
                val lineAvgY = line.map { it.centerY }.average()
                val yDifference = abs(element.centerY - lineAvgY)

                // If element is close enough vertically (within reasonable threshold)
                if (yDifference <= getLineHeightThreshold(line)) {
                    line.add(element)
                    addedToLine = true
                    break
                }
            }

            // If not added to any existing line, create new line
            if (!addedToLine) {
                lines.add(mutableListOf(element))
            }
        }

        return lines
    }

    private fun getLineHeightThreshold(line: List<TextElement>): Int {
        if (line.isEmpty()) return 25 // Reduced default threshold

        // Calculate average height of elements in the line
        val avgHeight = line.mapNotNull { it.boundingBox.height() }.average()

        // Use 50% of average height as threshold (more conservative), with tighter bounds
        return (avgHeight * 0.5).toInt().coerceIn(12, 35)
    }

    private data class TextElement(
        val text: String,
        val boundingBox: Rect,
        val centerY: Int,
        val centerX: Int
    )

}