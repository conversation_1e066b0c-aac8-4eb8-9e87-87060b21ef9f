package com.arrayberry.nowreceipt

import android.content.Context
import android.graphics.Bitmap
import android.net.Uri
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

class FileManager(private val context: Context) {
    
    private val receiptsDir: File by lazy {
        File(context.filesDir, "receipts").apply {
            if (!exists()) mkdirs()
        }
    }
    
    fun saveReceiptPhoto(bitmap: Bitmap): String? {
        return try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val filename = "receipt_$timestamp.jpg"
            val file = File(receiptsDir, filename)
            
            FileOutputStream(file).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
            }
            
            file.absolutePath
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
    
    fun saveReceiptPhoto(uri: Uri): String? {
        return try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val filename = "receipt_$timestamp.jpg"
            val file = File(receiptsDir, filename)
            
            context.contentResolver.openInputStream(uri)?.use { input ->
                FileOutputStream(file).use { output ->
                    input.copyTo(output)
                }
            }
            
            file.absolutePath
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
    
    fun deleteReceiptPhoto(photoPath: String?): Boolean {
        return try {
            if (photoPath != null) {
                val file = File(photoPath)
                if (file.exists()) {
                    file.delete()
                } else {
                    true // File doesn't exist, consider as deleted
                }
            } else {
                true
            }
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }
    
    fun getReceiptPhotoFile(photoPath: String?): File? {
        return if (photoPath != null) {
            val file = File(photoPath)
            if (file.exists()) file else null
        } else {
            null
        }
    }
    
    fun exportReceiptData(receipt: ReceiptEntity): String? {
        return try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val filename = "receipt_data_$timestamp.txt"
            val file = File(receiptsDir, filename)
            
            val content = buildString {
                appendLine("Receipt Data Export")
                appendLine("===================")
                appendLine("Generated: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}")
                appendLine()
                appendLine("Merchant: ${receipt.merchantName ?: "Not detected"}")
                appendLine("Date: ${receipt.date ?: "Not detected"}")
                appendLine("Amount: ${receipt.amount ?: "Not detected"}")
                appendLine("Photo: ${receipt.photoPath ?: "No photo"}")
                appendLine("Created: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(receipt.createdAt)}")
                appendLine()
                appendLine("Raw Text:")
                appendLine("---------")
                appendLine(receipt.rawText)
            }
            
            file.writeText(content)
            file.absolutePath
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }
}