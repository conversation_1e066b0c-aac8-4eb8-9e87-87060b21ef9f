package com.arrayberry.nowreceipt

import java.util.regex.Pattern

data class FieldCandidate(
    val text: String,
    val confidence: Float,
    val lineIndex: Int,
    val position: Int
)

class TextProcessor {
    
    companion object {
        // Common business entity types for merchant identification
        private val BUSINESS_SUFFIXES = setOf(
            "LLC", "INC", "CORP", "LTD", "CO", "COMPANY", "RESTAURANT", "CAFE", "STORE", 
            "SHOP", "MARKET", "PHARMACY", "HOTEL", "MOTEL", "BAR", "PUB", "GRILL"
        )
        
        // Common receipt header words to filter out
        private val RECEIPT_HEADERS = setOf(
            "RECEIPT", "INVOICE", "BILL", "TICKET", "ORDER", "PURCHASE", "TRANSACTION",
            "COPY", "CUSTOMER", "MERCHANT", "STORE", "LOCATION", "TERMINAL"
        )
        
        // Date separators and formats
        private val DATE_SEPARATORS = listOf("/", "-", ".", " ")
        private val MONTH_NAMES = mapOf(
            "JAN" to "01", "FEB" to "02", "MAR" to "03", "APR" to "04",
            "MAY" to "05", "JUN" to "06", "JUL" to "07", "AUG" to "08",
            "SEP" to "09", "OCT" to "10", "NOV" to "11", "DEC" to "12",
            "JANUARY" to "01", "FEBRUARY" to "02", "MARCH" to "03", "APRIL" to "04",
            "JUNE" to "06", "JULY" to "07", "AUGUST" to "08", "SEPTEMBER" to "09",
            "OCTOBER" to "10", "NOVEMBER" to "11", "DECEMBER" to "12"
        )
    }
    
    fun preprocessText(text: String): List<String> {
        return text.split("\n")
            .map { line -> line.trim() }
            .filter { line -> line.isNotEmpty() }
            .map { line -> cleanLine(line) }
    }
    
    private fun cleanLine(line: String): String {
        // Remove excessive whitespace and normalize
        return line.replace(Regex("\\s+"), " ")
            .replace(Regex("[^\\w\\s\\$\\.\\-/:]"), " ")
            .trim()
    }
    
    fun extractMerchantCandidates(lines: List<String>): List<FieldCandidate> {
        val candidates = mutableListOf<FieldCandidate>()
        
        // Analyze first 5 lines for merchant names
        for (i in 0..minOf(4, lines.size - 1)) {
            val line = lines[i]
            if (line.length < 3) continue
            
            val confidence = calculateMerchantConfidence(line, i, lines)
            if (confidence > 0.3f) {
                candidates.add(FieldCandidate(line, confidence, i, 0))
            }
        }
        
        return candidates.sortedByDescending { it.confidence }
    }
    
    private fun calculateMerchantConfidence(line: String, lineIndex: Int, allLines: List<String>): Float {
        var confidence = 0.5f
        val upperLine = line.uppercase()
        
        // Higher confidence for earlier lines
        confidence += (5 - lineIndex) * 0.1f
        
        // Check for business suffixes
        if (BUSINESS_SUFFIXES.any { suffix -> upperLine.contains(suffix) }) {
            confidence += 0.3f
        }
        
        // Penalty for receipt headers
        if (RECEIPT_HEADERS.any { header -> upperLine.contains(header) }) {
            confidence -= 0.4f
        }
        
        // Penalty for lines with dates or amounts
        if (containsDate(line) || containsAmount(line)) {
            confidence -= 0.3f
        }
        
        // Bonus for alphanumeric content (typical business names)
        if (line.matches(Regex(".*[A-Za-z].*[A-Za-z].*"))) {
            confidence += 0.2f
        }
        
        // Penalty for very short or very long lines
        when {
            line.length < 5 -> confidence -= 0.2f
            line.length > 40 -> confidence -= 0.1f
            line.length in 8..25 -> confidence += 0.1f
        }
        
        return confidence.coerceIn(0f, 1f)
    }
    
    fun extractDateCandidates(lines: List<String>): List<FieldCandidate> {
        val candidates = mutableListOf<FieldCandidate>()
        
        lines.forEachIndexed { index, line ->
            val dates = findDatesInLine(line)
            dates.forEach { date ->
                val confidence = calculateDateConfidence(date, index)
                candidates.add(FieldCandidate(date, confidence, index, 0))
            }
        }
        
        return candidates.sortedByDescending { it.confidence }
    }
    
    private fun findDatesInLine(line: String): List<String> {
        val dates = mutableListOf<String>()
        
        // Various date patterns
        val patterns = listOf(
            Pattern.compile("\\b\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{4}\\b"), // MM/dd/yyyy
            Pattern.compile("\\b\\d{4}[/\\-.]\\d{1,2}[/\\-.]\\d{1,2}\\b"), // yyyy/MM/dd
            Pattern.compile("\\b\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{2}\\b"), // MM/dd/yy
            Pattern.compile("\\b(${MONTH_NAMES.keys.joinToString("|")})\\s+\\d{1,2},?\\s+\\d{4}\\b", Pattern.CASE_INSENSITIVE), // Month dd, yyyy
            Pattern.compile("\\b\\d{1,2}\\s+(${MONTH_NAMES.keys.joinToString("|")})\\s+\\d{4}\\b", Pattern.CASE_INSENSITIVE) // dd Month yyyy
        )
        
        patterns.forEach { pattern ->
            val matcher = pattern.matcher(line)
            while (matcher.find()) {
                dates.add(matcher.group())
            }
        }
        
        return dates
    }
    
    private fun calculateDateConfidence(date: String, lineIndex: Int): Float {
        var confidence = 0.6f
        
        // Higher confidence for dates in first half of receipt
        if (lineIndex < 10) confidence += 0.2f
        
        // Check date format validity
        if (isValidDateFormat(date)) confidence += 0.3f
        
        return confidence.coerceIn(0f, 1f)
    }
    
    fun extractAmountCandidates(lines: List<String>): List<FieldCandidate> {
        val candidates = mutableListOf<FieldCandidate>()

        lines.forEachIndexed { index, line ->
            val amounts = findAmountsInLine(line)
            amounts.forEach { amount ->
                val confidence = calculateAmountConfidence(amount, line, index, lines)
                candidates.add(FieldCandidate(amount, confidence, index, 0))
            }
        }

        // Sort by confidence and apply additional filtering
        val sortedCandidates = candidates.sortedByDescending { it.confidence }

        // Apply post-processing to improve selection
        return applyAmountPostProcessing(sortedCandidates, lines)
    }
    
    private fun findAmountsInLine(line: String): List<String> {
        val amounts = mutableListOf<String>()
        
        val patterns = listOf(
            Pattern.compile("\\$\\d+\\.\\d{2}"), // $XX.XX
            Pattern.compile("\\b\\d+\\.\\d{2}\\b"), // XX.XX
            Pattern.compile("\\$\\d+,\\d{3}\\.\\d{2}"), // $X,XXX.XX
            Pattern.compile("\\b\\d+,\\d{3}\\.\\d{2}\\b") // X,XXX.XX
        )
        
        patterns.forEach { pattern ->
            val matcher = pattern.matcher(line)
            while (matcher.find()) {
                amounts.add(matcher.group())
            }
        }
        
        return amounts
    }
    
    private fun calculateAmountConfidence(amount: String, line: String, lineIndex: Int, allLines: List<String>): Float {
        var confidence = 0.4f
        val upperLine = line.uppercase()
        val amountValue = amount.replace("$", "").replace(",", "").toDoubleOrNull() ?: 0.0

        // Higher confidence for larger amounts (likely totals)
        when {
            amountValue > 100 -> confidence += 0.3f
            amountValue > 20 -> confidence += 0.2f
            amountValue < 1 -> confidence -= 0.3f
        }

        // Enhanced total indicators - more comprehensive list
        val totalIndicators = listOf(
            "TOTAL", "AMOUNT", "BALANCE", "DUE", "CHARGE", "PAYMENT",
            "GRAND TOTAL", "FINAL TOTAL", "NET TOTAL", "AMOUNT DUE",
            "TOTAL AMOUNT", "FINAL AMOUNT", "AMOUNT OWING", "PAYABLE",
            "TOTAL DUE", "AMOUNT PAYABLE", "FINAL CHARGE", "NET AMOUNT"
        )
        if (totalIndicators.any { indicator -> upperLine.contains(indicator) }) {
            confidence += 0.5f // Increased from 0.4f
        }

        // Subtotal indicators - significantly reduce confidence
        val subtotalIndicators = listOf(
            "SUBTOTAL", "SUB TOTAL", "SUB-TOTAL", "SUBTOT", "SUB TOT",
            "MERCHANDISE TOTAL", "ITEM TOTAL", "GOODS TOTAL", "PRODUCT TOTAL"
        )
        if (subtotalIndicators.any { indicator -> upperLine.contains(indicator) }) {
            confidence -= 0.6f // Strong penalty for subtotals
        }

        // Look for tax indicators (usually not the main total)
        val taxIndicators = listOf("TAX", "HST", "GST", "PST", "VAT", "SALES TAX", "STATE TAX")
        if (taxIndicators.any { indicator -> upperLine.contains(indicator) }) {
            confidence -= 0.3f // Increased penalty from 0.2f
        }

        // Contextual analysis - check for amount relationships
        confidence += analyzeAmountContext(amountValue, lineIndex, allLines)

        // Prefer amounts in the latter half of the receipt, but not too much
        if (lineIndex > allLines.size / 2) {
            confidence += 0.15f // Reduced from 0.2f to be less aggressive
        }

        // Bonus for amounts that appear to be final totals based on position
        if (lineIndex > allLines.size * 0.75) {
            confidence += 0.1f
        }

        return confidence.coerceIn(0f, 1f)
    }

    private fun analyzeAmountContext(currentAmount: Double, lineIndex: Int, allLines: List<String>): Float {
        var contextBonus = 0f

        // Look for subtotal + tax = total pattern
        val nearbyLines = allLines.subList(
            maxOf(0, lineIndex - 3),
            minOf(allLines.size, lineIndex + 4)
        )

        val nearbyAmounts = mutableListOf<Pair<Double, String>>()
        nearbyLines.forEach { line ->
            val amounts = findAmountsInLine(line)
            amounts.forEach { amount ->
                val value = amount.replace("$", "").replace(",", "").toDoubleOrNull()
                if (value != null) {
                    nearbyAmounts.add(Pair(value, line.uppercase()))
                }
            }
        }

        // Check if this amount could be a total (subtotal + tax)
        for (i in nearbyAmounts.indices) {
            for (j in nearbyAmounts.indices) {
                if (i != j) {
                    val amount1 = nearbyAmounts[i].first
                    val amount2 = nearbyAmounts[j].first
                    val line1 = nearbyAmounts[i].second
                    val line2 = nearbyAmounts[j].second

                    // Check if current amount equals sum of two other amounts (subtotal + tax)
                    if (Math.abs(currentAmount - (amount1 + amount2)) < 0.01) {
                        // Verify one looks like subtotal and other like tax
                        val hasSubtotal = line1.contains("SUBTOTAL") || line1.contains("SUB TOTAL") ||
                                         line2.contains("SUBTOTAL") || line2.contains("SUB TOTAL")
                        val hasTax = line1.contains("TAX") || line2.contains("TAX")

                        if (hasSubtotal && hasTax) {
                            contextBonus += 0.4f // Strong bonus for being sum of subtotal + tax
                        }
                    }
                }
            }
        }

        // Check if this amount appears after subtotal and tax lines
        val precedingLines = allLines.subList(0, minOf(lineIndex + 1, allLines.size))
        val hasSubtotalBefore = precedingLines.any { line ->
            val upper = line.uppercase()
            upper.contains("SUBTOTAL") || upper.contains("SUB TOTAL") || upper.contains("SUB-TOTAL")
        }
        val hasTaxBefore = precedingLines.any { line ->
            val upper = line.uppercase()
            upper.contains("TAX") && !upper.contains("SUBTOTAL")
        }

        if (hasSubtotalBefore && hasTaxBefore) {
            contextBonus += 0.2f // Bonus for appearing after both subtotal and tax
        }

        return contextBonus
    }

    private fun applyAmountPostProcessing(candidates: List<FieldCandidate>, allLines: List<String>): List<FieldCandidate> {
        if (candidates.isEmpty()) return candidates

        val processedCandidates = candidates.toMutableList()

        // Find potential subtotal-tax-total relationships
        val amountGroups = findAmountRelationships(candidates, allLines)

        // Boost confidence for amounts that are clearly final totals
        amountGroups.forEach { group ->
            if (group.isValidTotalGroup()) {
                // Find the total in our candidates and boost its confidence
                val totalCandidate = processedCandidates.find { candidate ->
                    val value = candidate.text.replace("$", "").replace(",", "").toDoubleOrNull()
                    value != null && Math.abs(value - group.total) < 0.01
                }

                totalCandidate?.let { candidate ->
                    val index = processedCandidates.indexOf(candidate)
                    if (index >= 0) {
                        processedCandidates[index] = candidate.copy(confidence = minOf(1.0f, candidate.confidence + 0.3f))
                    }
                }

                // Reduce confidence for the subtotal
                val subtotalCandidate = processedCandidates.find { candidate ->
                    val value = candidate.text.replace("$", "").replace(",", "").toDoubleOrNull()
                    value != null && Math.abs(value - group.subtotal) < 0.01
                }

                subtotalCandidate?.let { candidate ->
                    val index = processedCandidates.indexOf(candidate)
                    if (index >= 0) {
                        processedCandidates[index] = candidate.copy(confidence = maxOf(0.0f, candidate.confidence - 0.4f))
                    }
                }
            }
        }

        return processedCandidates.sortedByDescending { it.confidence }
    }

    private fun findAmountRelationships(candidates: List<FieldCandidate>, allLines: List<String>): List<AmountGroup> {
        val groups = mutableListOf<AmountGroup>()
        val amounts = candidates.mapNotNull { candidate ->
            val value = candidate.text.replace("$", "").replace(",", "").toDoubleOrNull()
            if (value != null) {
                AmountInfo(value, candidate.lineIndex, allLines[candidate.lineIndex].uppercase())
            } else null
        }

        // Look for subtotal + tax = total patterns
        for (i in amounts.indices) {
            for (j in amounts.indices) {
                for (k in amounts.indices) {
                    if (i != j && j != k && i != k) {
                        val amount1 = amounts[i]
                        val amount2 = amounts[j]
                        val amount3 = amounts[k]

                        // Check if amount3 = amount1 + amount2
                        if (Math.abs(amount3.value - (amount1.value + amount2.value)) < 0.01) {
                            val group = AmountGroup(amount1, amount2, amount3)
                            if (group.isValidTotalGroup()) {
                                groups.add(group)
                            }
                        }
                    }
                }
            }
        }

        return groups
    }

    private data class AmountInfo(val value: Double, val lineIndex: Int, val lineText: String)

    private data class AmountGroup(val amount1: AmountInfo, val amount2: AmountInfo, val amount3: AmountInfo) {
        val subtotal: Double
        val tax: Double
        val total: Double

        init {
            // Determine which is subtotal, tax, and total based on line content and values
            when {
                amount1.lineText.contains("SUBTOTAL") || amount1.lineText.contains("SUB TOTAL") -> {
                    subtotal = amount1.value
                    if (amount2.lineText.contains("TAX")) {
                        tax = amount2.value
                        total = amount3.value
                    } else {
                        tax = amount3.value
                        total = amount2.value
                    }
                }
                amount2.lineText.contains("SUBTOTAL") || amount2.lineText.contains("SUB TOTAL") -> {
                    subtotal = amount2.value
                    if (amount1.lineText.contains("TAX")) {
                        tax = amount1.value
                        total = amount3.value
                    } else {
                        tax = amount3.value
                        total = amount1.value
                    }
                }
                amount3.lineText.contains("SUBTOTAL") || amount3.lineText.contains("SUB TOTAL") -> {
                    subtotal = amount3.value
                    if (amount1.lineText.contains("TAX")) {
                        tax = amount1.value
                        total = amount2.value
                    } else {
                        tax = amount2.value
                        total = amount1.value
                    }
                }
                else -> {
                    // Fallback: assume largest is total, smallest is tax, middle is subtotal
                    val sorted = listOf(amount1.value, amount2.value, amount3.value).sorted()
                    subtotal = sorted[1]
                    tax = sorted[0]
                    total = sorted[2]
                }
            }
        }

        fun isValidTotalGroup(): Boolean {
            // Check if this looks like a valid subtotal-tax-total relationship
            val hasSubtotalKeyword = amount1.lineText.contains("SUBTOTAL") || amount1.lineText.contains("SUB TOTAL") ||
                                   amount2.lineText.contains("SUBTOTAL") || amount2.lineText.contains("SUB TOTAL") ||
                                   amount3.lineText.contains("SUBTOTAL") || amount3.lineText.contains("SUB TOTAL")

            val hasTaxKeyword = amount1.lineText.contains("TAX") ||
                              amount2.lineText.contains("TAX") ||
                              amount3.lineText.contains("TAX")

            val hasTotalKeyword = amount1.lineText.contains("TOTAL") ||
                                amount2.lineText.contains("TOTAL") ||
                                amount3.lineText.contains("TOTAL")

            // Tax should be reasonable percentage of subtotal (1% to 20%)
            val taxPercentage = if (subtotal > 0) (tax / subtotal) * 100 else 0.0
            val reasonableTaxRate = taxPercentage in 1.0..20.0

            return (hasSubtotalKeyword || hasTaxKeyword || hasTotalKeyword) && reasonableTaxRate
        }
    }

    private fun containsDate(text: String): Boolean {
        return text.matches(Regex(".*\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{2,4}.*"))
    }
    
    private fun containsAmount(text: String): Boolean {
        return text.matches(Regex(".*\\$\\d+\\.\\d{2}.*"))
    }
    
    private fun isValidDateFormat(date: String): Boolean {
        // Basic validation - could be enhanced with actual date parsing
        return date.matches(Regex("\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{2,4}")) ||
               date.matches(Regex("\\d{4}[/\\-.]\\d{1,2}[/\\-.]\\d{1,2}"))
    }
}