package com.arrayberry.nowreceipt

import java.util.regex.Pattern

data class FieldCandidate(
    val text: String,
    val confidence: Float,
    val lineIndex: Int,
    val position: Int
)

class TextProcessor {
    
    companion object {
        // Common business entity types for merchant identification
        private val BUSINESS_SUFFIXES = setOf(
            "LLC", "INC", "CORP", "LTD", "CO", "COMPANY", "RESTAURANT", "CAFE", "STORE", 
            "SHOP", "MARKET", "PHARMACY", "HOTEL", "MOTEL", "BAR", "PUB", "GRILL"
        )
        
        // Common receipt header words to filter out
        private val RECEIPT_HEADERS = setOf(
            "RECEIPT", "INVOICE", "BILL", "TICKET", "ORDER", "PURCHASE", "TRANSACTION",
            "COPY", "CUSTOMER", "MERCHANT", "STORE", "LOCATION", "TERMINAL"
        )
        
        // Date separators and formats
        private val DATE_SEPARATORS = listOf("/", "-", ".", " ")
        private val MONTH_NAMES = mapOf(
            "JAN" to "01", "FEB" to "02", "MAR" to "03", "APR" to "04",
            "MAY" to "05", "JUN" to "06", "JUL" to "07", "AUG" to "08",
            "SEP" to "09", "OCT" to "10", "NOV" to "11", "DEC" to "12",
            "JANUARY" to "01", "FEBRUARY" to "02", "MARCH" to "03", "APRIL" to "04",
            "JUNE" to "06", "JULY" to "07", "AUGUST" to "08", "SEPTEMBER" to "09",
            "OCTOBER" to "10", "NOVEMBER" to "11", "DECEMBER" to "12"
        )
    }
    
    fun preprocessText(text: String): List<String> {
        return text.split("\n")
            .map { line -> line.trim() }
            .filter { line -> line.isNotEmpty() }
            .map { line -> cleanLine(line) }
    }
    
    private fun cleanLine(line: String): String {
        // Remove excessive whitespace and normalize
        return line.replace(Regex("\\s+"), " ")
            .replace(Regex("[^\\w\\s\\$\\.\\-/:]"), " ")
            .trim()
    }
    
    fun extractMerchantCandidates(lines: List<String>): List<FieldCandidate> {
        val candidates = mutableListOf<FieldCandidate>()
        
        // Analyze first 5 lines for merchant names
        for (i in 0..minOf(4, lines.size - 1)) {
            val line = lines[i]
            if (line.length < 3) continue
            
            val confidence = calculateMerchantConfidence(line, i, lines)
            if (confidence > 0.3f) {
                candidates.add(FieldCandidate(line, confidence, i, 0))
            }
        }
        
        return candidates.sortedByDescending { it.confidence }
    }
    
    private fun calculateMerchantConfidence(line: String, lineIndex: Int, allLines: List<String>): Float {
        var confidence = 0.5f
        val upperLine = line.uppercase()
        
        // Higher confidence for earlier lines
        confidence += (5 - lineIndex) * 0.1f
        
        // Check for business suffixes
        if (BUSINESS_SUFFIXES.any { suffix -> upperLine.contains(suffix) }) {
            confidence += 0.3f
        }
        
        // Penalty for receipt headers
        if (RECEIPT_HEADERS.any { header -> upperLine.contains(header) }) {
            confidence -= 0.4f
        }
        
        // Penalty for lines with dates or amounts
        if (containsDate(line) || containsAmount(line)) {
            confidence -= 0.3f
        }
        
        // Bonus for alphanumeric content (typical business names)
        if (line.matches(Regex(".*[A-Za-z].*[A-Za-z].*"))) {
            confidence += 0.2f
        }
        
        // Penalty for very short or very long lines
        when {
            line.length < 5 -> confidence -= 0.2f
            line.length > 40 -> confidence -= 0.1f
            line.length in 8..25 -> confidence += 0.1f
        }
        
        return confidence.coerceIn(0f, 1f)
    }
    
    fun extractDateCandidates(lines: List<String>): List<FieldCandidate> {
        val candidates = mutableListOf<FieldCandidate>()
        
        lines.forEachIndexed { index, line ->
            val dates = findDatesInLine(line)
            dates.forEach { date ->
                val confidence = calculateDateConfidence(date, index)
                candidates.add(FieldCandidate(date, confidence, index, 0))
            }
        }
        
        return candidates.sortedByDescending { it.confidence }
    }
    
    private fun findDatesInLine(line: String): List<String> {
        val dates = mutableListOf<String>()
        
        // Various date patterns
        val patterns = listOf(
            Pattern.compile("\\b\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{4}\\b"), // MM/dd/yyyy
            Pattern.compile("\\b\\d{4}[/\\-.]\\d{1,2}[/\\-.]\\d{1,2}\\b"), // yyyy/MM/dd
            Pattern.compile("\\b\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{2}\\b"), // MM/dd/yy
            Pattern.compile("\\b(${MONTH_NAMES.keys.joinToString("|")})\\s+\\d{1,2},?\\s+\\d{4}\\b", Pattern.CASE_INSENSITIVE), // Month dd, yyyy
            Pattern.compile("\\b\\d{1,2}\\s+(${MONTH_NAMES.keys.joinToString("|")})\\s+\\d{4}\\b", Pattern.CASE_INSENSITIVE) // dd Month yyyy
        )
        
        patterns.forEach { pattern ->
            val matcher = pattern.matcher(line)
            while (matcher.find()) {
                dates.add(matcher.group())
            }
        }
        
        return dates
    }
    
    private fun calculateDateConfidence(date: String, lineIndex: Int): Float {
        var confidence = 0.6f
        
        // Higher confidence for dates in first half of receipt
        if (lineIndex < 10) confidence += 0.2f
        
        // Check date format validity
        if (isValidDateFormat(date)) confidence += 0.3f
        
        return confidence.coerceIn(0f, 1f)
    }
    
    fun extractAmountCandidates(lines: List<String>): List<FieldCandidate> {
        val candidates = mutableListOf<FieldCandidate>()
        
        lines.forEachIndexed { index, line ->
            val amounts = findAmountsInLine(line)
            amounts.forEach { amount ->
                val confidence = calculateAmountConfidence(amount, line, index, lines)
                candidates.add(FieldCandidate(amount, confidence, index, 0))
            }
        }
        
        return candidates.sortedByDescending { it.confidence }
    }
    
    private fun findAmountsInLine(line: String): List<String> {
        val amounts = mutableListOf<String>()
        
        val patterns = listOf(
            Pattern.compile("\\$\\d+\\.\\d{2}"), // $XX.XX
            Pattern.compile("\\b\\d+\\.\\d{2}\\b"), // XX.XX
            Pattern.compile("\\$\\d+,\\d{3}\\.\\d{2}"), // $X,XXX.XX
            Pattern.compile("\\b\\d+,\\d{3}\\.\\d{2}\\b") // X,XXX.XX
        )
        
        patterns.forEach { pattern ->
            val matcher = pattern.matcher(line)
            while (matcher.find()) {
                amounts.add(matcher.group())
            }
        }
        
        return amounts
    }
    
    private fun calculateAmountConfidence(amount: String, line: String, lineIndex: Int, allLines: List<String>): Float {
        var confidence = 0.4f
        val upperLine = line.uppercase()
        val amountValue = amount.replace("$", "").replace(",", "").toDoubleOrNull() ?: 0.0
        
        // Higher confidence for larger amounts (likely totals)
        when {
            amountValue > 100 -> confidence += 0.3f
            amountValue > 20 -> confidence += 0.2f
            amountValue < 1 -> confidence -= 0.3f
        }
        
        // Look for total indicators
        val totalIndicators = listOf("TOTAL", "AMOUNT", "BALANCE", "DUE", "CHARGE", "PAYMENT")
        if (totalIndicators.any { indicator -> upperLine.contains(indicator) }) {
            confidence += 0.4f
        }
        
        // Look for tax indicators (usually not the main total)
        val taxIndicators = listOf("TAX", "HST", "GST", "PST", "VAT")
        if (taxIndicators.any { indicator -> upperLine.contains(indicator) }) {
            confidence -= 0.2f
        }
        
        // Prefer amounts in the latter half of the receipt
        if (lineIndex > allLines.size / 2) {
            confidence += 0.2f
        }
        
        return confidence.coerceIn(0f, 1f)
    }
    
    private fun containsDate(text: String): Boolean {
        return text.matches(Regex(".*\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{2,4}.*"))
    }
    
    private fun containsAmount(text: String): Boolean {
        return text.matches(Regex(".*\\$\\d+\\.\\d{2}.*"))
    }
    
    private fun isValidDateFormat(date: String): Boolean {
        // Basic validation - could be enhanced with actual date parsing
        return date.matches(Regex("\\d{1,2}[/\\-.]\\d{1,2}[/\\-.]\\d{2,4}")) ||
               date.matches(Regex("\\d{4}[/\\-.]\\d{1,2}[/\\-.]\\d{1,2}"))
    }
}