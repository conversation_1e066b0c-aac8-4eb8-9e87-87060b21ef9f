package com.arrayberry.nowreceipt

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "receipts")
data class ReceiptEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val merchantName: String?,
    val date: String?,
    val amount: String?,
    val rawText: String,
    val photoPath: String?,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
)