# Dollarama Receipt Processing Fixes

## Issues Identified from the Receipt Image

Looking at the Dollarama receipt, several specific problems were identified:

### Receipt Content:
```
DOLLARAMA
11700 Yonge Street Unit 2
Richmond Hill ON L4E 0K4
HST 863624433
...
SUBTOTAL         $15.75
HST 13%          $2.05
TOTAL            $17.80
AMEX             $17.80
```

### Problems:
1. **Merchant Name**: Should detect "DOLLARAMA" but was failing
2. **Amount**: Should detect "$17.80" from TOTAL line, not subtotal "$15.75"
3. **Text Reconstruction**: Line fragmentation affecting field detection

## Fixes Implemented

### 1. Enhanced Merchant Detection

#### Added Known Merchant Recognition
```kotlin
val knownMerchants = setOf(
    "DOLLARAMA", "WALMART", "COST<PERSON>", "LOBLAWS", "METRO", "SOBEYS", "SHOPPERS",
    "CANADIAN TIRE", "HOME DEPOT", "IKEA", "BEST BUY", "STAPLES", "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "T<PERSON> HORTONS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PIZZA HUT", "KFC", "A&W"
)
if (knownMerchants.any { merchant -> upperLine.contains(merchant) }) {
    confidence += 0.6f // Strong bonus for known merchants
}
```

#### Improved Address Filtering
```kotlin
// Penalty for address-like content
if (upperLine.matches(Regex(".*\\d+.*STREET.*")) || 
    upperLine.matches(Regex(".*\\d+.*ST\\b.*")) ||
    upperLine.matches(Regex(".*\\d+.*AVE.*"))) {
    confidence -= 0.5f // Strong penalty for addresses
}
```

#### Enhanced Business Name Recognition
```kotlin
// Bonus for all-caps business names (common on receipts)
if (line.matches(Regex("^[A-Z\\s]+$")) && line.length >= 5) {
    confidence += 0.3f
}
```

### 2. Improved Amount Detection

#### Stronger TOTAL Line Recognition
```kotlin
// Special handling for lines that start with "TOTAL" (very strong indicator)
if (upperLine.trim().startsWith("TOTAL")) {
    confidence += 0.8f // Very strong bonus for lines starting with TOTAL
} else if (totalIndicators.any { indicator -> upperLine.contains(indicator) }) {
    confidence += 0.5f // Regular bonus for containing total indicators
}
```

#### Better Amount Pattern Matching
```kotlin
val patterns = listOf(
    Pattern.compile("\\$\\d+\\.\\d{2}"), // $XX.XX
    Pattern.compile("\\$\\d+,\\d{3}\\.\\d{2}"), // $X,XXX.XX
    Pattern.compile("\\$\\d+,\\d{3},\\d{3}\\.\\d{2}"), // $X,XXX,XXX.XX
    Pattern.compile("\\b\\d+\\.\\d{2}\\b"), // XX.XX (standalone)
    Pattern.compile("\\b\\d+,\\d{3}\\.\\d{2}\\b"), // X,XXX.XX (standalone)
    Pattern.compile("\\b\\d{1,3},\\d{3},\\d{3}\\.\\d{2}\\b") // X,XXX,XXX.XX (standalone)
)
```

#### Amount Validation
```kotlin
// Filter out obviously wrong amounts (like dates that look like amounts)
val numericValue = amount.replace("$", "").replace(",", "").toDoubleOrNull()
if (numericValue != null && numericValue >= 0.01 && numericValue <= 999999.99) {
    amounts.add(amount)
}
```

### 3. Refined Text Reconstruction

#### More Conservative Line Grouping
```kotlin
private fun getLineHeightThreshold(line: List<TextElement>): Int {
    if (line.isEmpty()) return 25 // Reduced default threshold
    
    // Calculate average height of elements in the line
    val avgHeight = line.mapNotNull { it.boundingBox.height() }.average()
    
    // Use 50% of average height as threshold (more conservative), with tighter bounds
    return (avgHeight * 0.5).toInt().coerceIn(12, 35)
}
```

### 4. Expanded Search Range

#### Increased Merchant Search Range
```kotlin
// Analyze first 8 lines for merchant names (increased from 5)
for (i in 0..minOf(7, lines.size - 1)) {
    val line = lines[i]
    if (line.length < 3) continue
    
    val confidence = calculateMerchantConfidence(line, i, lines)
    if (confidence > 0.2f) { // Lowered threshold from 0.3f
        candidates.add(FieldCandidate(line, confidence, i, 0))
    }
}
```

## Expected Results for Dollarama Receipt

### Before Fixes:
- **Merchant**: Might pick address line or miss entirely
- **Amount**: Might pick "$15.75" (subtotal) instead of "$17.80" (total)

### After Fixes:
- **Merchant**: Should correctly identify "DOLLARAMA"
- **Amount**: Should correctly identify "$17.80" from the TOTAL line

## Test Case Added

```kotlin
@Test
fun testDollaramaReceipt_CorrectMerchantAndAmount() {
    val dollaramaReceiptText = """
        DOLLARAMA
        11700 Yonge Street Unit 2
        Richmond Hill ON L4E 0K4
        HST 863624433
        FOAM BOARD         667888505333  4.00 H
        PINS               667888414277  1.50 H
        PINS               667888297535  4.00 H
        BALLOONS           667888464968  4.25 H
        BALLOON KIT        667888594245  2.00 H
        SUBTOTAL                         $15.75
        HST 13%                          $2.05
        TOTAL                            $17.80
        AMEX                             $17.80
        TYPE: PURCHASE
        ACCT: AMERICAN EXPRESS
        AMOUNT:              $17.80
        CARD NUMBER:         xxxxxxxxxx4424
        DATE/TIME:           25/06/05 19:30:51
    """.trimIndent()
    
    val lines = textProcessor.preprocessText(dollaramaReceiptText)
    
    // Test merchant detection
    val merchantCandidates = textProcessor.extractMerchantCandidates(lines)
    val topMerchant = merchantCandidates.first().text
    assertEquals("Should detect DOLLARAMA as merchant", "DOLLARAMA", topMerchant)
    
    // Test amount detection
    val amountCandidates = textProcessor.extractAmountCandidates(lines)
    val topAmount = amountCandidates.first().text.replace("$", "").replace(",", "").toDoubleOrNull()
    assertEquals("Should detect $17.80 as the total amount", 17.80, topAmount ?: 0.0, 0.01)
}
```

## Debug Capability Added

Added `debugMerchantExtraction()` method to help troubleshoot merchant detection issues:

```kotlin
fun debugMerchantExtraction(lines: List<String>): String {
    // Returns detailed debug information about merchant detection process
}
```

## Summary

These fixes specifically target the issues seen in the Dollarama receipt:

1. **Known Merchant Recognition**: "DOLLARAMA" gets a strong confidence boost
2. **Address Filtering**: Address lines get penalized to avoid confusion
3. **TOTAL Line Priority**: Lines starting with "TOTAL" get maximum confidence
4. **Better Pattern Matching**: Improved amount detection patterns
5. **Conservative Reconstruction**: More accurate text line reconstruction

The combination of these improvements should correctly identify "DOLLARAMA" as the merchant and "$17.80" as the amount from the TOTAL line.
