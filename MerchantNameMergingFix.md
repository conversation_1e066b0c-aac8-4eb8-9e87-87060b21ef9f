# Fix for Merchant Name Merging Issue

## Problem Identified

The merchant name was being extracted as `'DOLLARAMA SUBTOTAL 15.75'` instead of just `'DOLLARAMA'`. This happened because:

1. **Over-aggressive merging**: The `shouldMergeLines()` function was merging lines that shouldn't be combined
2. **No keyword protection**: Important receipt keywords weren't protected from merging
3. **Insufficient filtering**: Merchant candidates weren't filtered to exclude mixed content

## Root Cause Analysis

### The Merging Problem
```kotlin
// OLD (Problematic) Logic
private fun shouldMergeLines(line1: String, line2: String): <PERSON><PERSON>an {
    // This would merge "DOLLARAMA" with "SUBTOTAL 15.75"
    if (line1.matches(Regex(".*[A-Za-z]$")) && line2.matches(Regex("^[A-Za-z].*"))) {
        return true  // TOO AGGRESSIVE!
    }
}
```

### The Reconstruction Problem
The `reconstructKnownPatterns()` method was creating proper lines, but then `mergeRemainingFragments()` was incorrectly combining them.

## Comprehensive Fixes Implemented

### 1. Enhanced Line Merging Protection

#### Keyword Protection
```kotlin
private fun shouldMergeLines(line1: String, line2: String): Boolean {
    // Don't merge if either line contains important receipt keywords
    val importantKeywords = setOf("DOLLARAMA", "SUBTOTAL", "TOTAL", "HST", "TAX", "AMEX", "AMOUNT")
    val line1Upper = line1.uppercase()
    val line2Upper = line2.uppercase()
    
    if (importantKeywords.any { keyword -> line1Upper.contains(keyword) || line2Upper.contains(keyword) }) {
        return false  // PROTECT IMPORTANT LINES
    }
}
```

#### Amount Protection
```kotlin
// Don't merge if either line already contains an amount
if (line1.contains(Regex("\\$?\\d+\\.\\d{2}")) || line2.contains(Regex("\\$?\\d+\\.\\d{2}"))) {
    return false
}
```

#### Conservative Merging
```kotlin
// Only merge very short fragments now
if (line1.length <= 8 && line2.length <= 8 && 
    line1.matches(Regex(".*[A-Za-z]$")) && line2.matches(Regex("^[A-Za-z].*"))) {
    return true
}
```

### 2. Improved Pattern Reconstruction

#### Better Tracking of Used Elements
```kotlin
private fun reconstructKnownPatterns(lines: List<String>): List<String> {
    val result = mutableListOf<String>()
    val used = BooleanArray(lines.size)
    
    // Reconstruct merchant name FIRST and mark as complete
    if (dollaramaFragments.isNotEmpty()) {
        result.add("DOLLARAMA")  // Clean, standalone line
        dollaramaFragments.forEach { used[it] = true }
    }
    
    // Then reconstruct other elements, checking used array
    val subtotalIndex = lines.indexOfFirst { !used[it] && it.uppercase().contains("SUBTOTAL") }
    // ...
}
```

#### Smarter Amount Finding
```kotlin
private fun findNearbyAmount(lines: List<String>, centerIndex: Int, used: BooleanArray): String? {
    // Look for amounts near the given index, prioritizing closer ones
    val searchRanges = listOf(
        (centerIndex - 1)..(centerIndex + 1),  // Very close first
        (centerIndex - 3)..(centerIndex + 3)   // Then nearby
    )
    
    for (range in searchRanges) {
        for (i in range) {
            if (i in 0 until lines.size && !used[i]) {
                val line = lines[i].trim()
                if (line.matches(Regex("\\$?\\d+\\.\\d{2}"))) {
                    return line.replace("$", "")
                }
            }
        }
    }
}
```

### 3. Enhanced Merchant Detection

#### Multi-Element Filtering
```kotlin
fun extractMerchantCandidates(lines: List<String>): List<FieldCandidate> {
    // ... extract candidates ...
    
    // Filter out candidates that contain multiple receipt elements
    val filteredCandidates = candidates.filter { candidate ->
        val line = candidate.text.uppercase()
        val elementCount = listOf("SUBTOTAL", "TOTAL", "HST", "TAX", "AMOUNT").count { line.contains(it) }
        elementCount <= 1 // Allow at most one receipt element keyword
    }
    
    return filteredCandidates.sortedByDescending { it.confidence }
}
```

#### Enhanced Confidence Penalties
```kotlin
// Strong penalty for lines containing multiple receipt keywords
val receiptKeywords = listOf("SUBTOTAL", "TOTAL", "HST", "TAX", "AMOUNT", "PAYMENT")
val keywordCount = receiptKeywords.count { keyword -> upperLine.contains(keyword) }
if (keywordCount > 1) {
    confidence -= 0.8f // Very strong penalty for mixed content
}

// Penalty for lines that contain amounts (likely not merchant names)
if (line.contains(Regex("\\d+\\.\\d{2}"))) {
    confidence -= 0.4f
}
```

## Expected Results

### Before Fix:
```
Merchant: "DOLLARAMA SUBTOTAL 15.75"
```

### After Fix:
```
Merchant: "DOLLARAMA"
```

### Reconstructed Text Structure:
```
DOLLARAMA
11700 Yonge Street Unit 2
Richmond Hill ON L4E 0K4
HST *********
SUBTOTAL 15.75
HST 13% 2.05
TOTAL 17.80
```

## Key Improvements

1. **Keyword Protection**: Important receipt terms are protected from merging
2. **Amount Protection**: Lines with amounts aren't merged with other content
3. **Conservative Merging**: Only very short fragments are merged
4. **Better Reconstruction**: Proper tracking of used elements prevents double-processing
5. **Enhanced Filtering**: Multi-element lines are filtered out of merchant candidates
6. **Stronger Penalties**: Lines with mixed content get heavily penalized

## Test Case Added

```kotlin
@Test
fun testMerchantExtractionDoesNotIncludeOtherElements() {
    val problematicText = """
        DOLLARAMA
        SUBTOTAL 15.75
        HST 13% 2.05
        TOTAL 17.80
    """.trimIndent()
    
    val lines = textProcessor.preprocessText(problematicText)
    val merchantCandidates = textProcessor.extractMerchantCandidates(lines)
    
    val topMerchant = merchantCandidates.first().text
    
    // Should be exactly "DOLLARAMA", not "DOLLARAMA SUBTOTAL 15.75"
    assertEquals("Should extract only DOLLARAMA", "DOLLARAMA", topMerchant)
    assertFalse("Should not contain SUBTOTAL", topMerchant.contains("SUBTOTAL"))
    assertFalse("Should not contain amounts", topMerchant.contains("15.75"))
}
```

The merchant name should now be correctly extracted as just "DOLLARAMA" without any additional content being merged into it.
