# Fixes for Scrambled Text Reconstruction

## Problem Analysis

The reconstructed text you showed was completely scrambled:

```
7964 Com
H
H H H H
2.00 $15.75 $2.05 $17.80 $17.80 H
4.00 1.50 4.00 4.25 arama.
DOLLARAMA arama.co
17.80 YOU Records FEES, APPLICABLE).
...
```

This indicates that the spatial text reconstruction algorithm was grouping text elements incorrectly, causing:
1. **Word fragmentation**: "DOLLARAMA" split into "DOLL" and "arama"
2. **Incorrect line grouping**: Unrelated text elements grouped together
3. **Lost context**: Important labels separated from their values

## Root Cause

The issue was in the `reconstructTextFromBlocks()` method:
1. **Over-aggressive grouping**: Elements were being grouped based on Y-coordinate proximity alone
2. **No horizontal overlap detection**: Text elements that shouldn't be on the same line were being merged
3. **No validation**: No check to ensure reconstruction quality

## Comprehensive Fixes Implemented

### 1. Improved Text Reconstruction Strategy

#### Primary Approach: Use ML Kit's Built-in Structure
```kotlin
private fun reconstructTextFromBlocks(visionText: Text): String {
    // Try using ML Kit's built-in line structure first
    val mlKitLines = mutableListOf<String>()
    
    for (block in visionText.textBlocks) {
        for (line in block.lines) {
            // Use ML Kit's line detection, but sort elements within each line
            val sortedElements = line.elements.sortedBy { element ->
                element.boundingBox?.centerX() ?: 0
            }
            val lineText = sortedElements.joinToString(" ") { it.text }
            if (lineText.trim().isNotEmpty()) {
                mlKitLines.add(lineText.trim())
            }
        }
    }
    
    // Sort lines by their vertical position and validate
    // If ML Kit structure seems reasonable, use it; otherwise fall back
}
```

#### Fallback: Custom Element Grouping with Overlap Detection
```kotlin
private fun groupElementsIntoLines(elements: List<TextElement>): List<List<TextElement>> {
    // Enhanced grouping with horizontal overlap detection
    for (element in sortedElements) {
        // Find the best matching line (closest Y-coordinate)
        // Additional check: ensure elements don't overlap horizontally too much
        val hasOverlap = line.any { existing ->
            val overlapWidth = maxOf(0, minOf(elementRight, existingRight) - maxOf(elementLeft, existingLeft))
            val elementWidth = elementRight - elementLeft
            overlapWidth > elementWidth * 0.5 // Prevent >50% overlap
        }
        
        if (!hasOverlap) {
            // Safe to add to this line
        }
    }
}
```

### 2. Reconstruction Quality Validation

#### Added Validation Method
```kotlin
private fun validateReconstruction(reconstructedText: String, originalText: String): Boolean {
    // Check if reconstruction preserved important content
    val keyTerms = listOf("DOLLARAMA", "TOTAL", "SUBTOTAL", "TAX", "HST", "AMOUNT")
    
    // Count preserved key terms
    val preservedTerms = keyTerms.count { term ->
        reconstructedUpper.contains(term) && originalUpper.contains(term)
    }
    
    // Check fragmentation ratio (too many very short lines)
    val fragmentationRatio = veryShortLines.toFloat() / reconstructedLines.size
    
    // Check amount preservation
    val amountPreservationRatio = reconstructedAmounts.intersect(originalAmounts).size.toFloat() / originalAmounts.size
    
    // Reconstruction is good if:
    // 1. Most key terms are preserved
    // 2. Not too fragmented (less than 30% very short lines)  
    // 3. Most amounts are preserved (at least 70%)
    return preservedTerms >= 2 && 
           fragmentationRatio < 0.3f && 
           amountPreservationRatio >= 0.7f
}
```

#### Fallback to Original Text
```kotlin
fun scanReceipt(bitmap: Bitmap, callback: (ReceiptData) -> Unit) {
    textRecognizer.process(image)
        .addOnSuccessListener { visionText ->
            val reconstructedText = reconstructTextFromBlocks(visionText)
            val isReconstructionGood = validateReconstruction(reconstructedText, visionText.text)
            
            val finalText = if (isReconstructionGood) {
                reconstructedText
            } else {
                // Fallback to original text if reconstruction is poor
                visionText.text
            }
            
            val receiptData = extractReceiptFieldsWithML(finalText)
            callback(receiptData)
        }
}
```

### 3. Enhanced Text Preprocessing

#### Fragment Merging for Original Text
```kotlin
private fun mergeFragmentedLines(lines: List<String>): List<String> {
    // Try to merge fragmented lines that belong together
    while (i < lines.size) {
        var currentLine = lines[i]
        
        // Look ahead to see if next lines should be merged
        while (j < lines.size && shouldMergeLines(currentLine, lines[j])) {
            currentLine += " " + lines[j]
            j++
        }
        
        mergedLines.add(currentLine)
        i = j
    }
}

private fun shouldMergeLines(line1: String, line2: String): Boolean {
    // Merge if line1 ends with a word and line2 starts with a word (potential word split)
    if (line1.matches(Regex(".*[A-Za-z]$")) && line2.matches(Regex("^[A-Za-z].*"))) {
        return true
    }
    
    // Merge if line1 is a label and line2 is an amount
    if (line1.matches(Regex(".*[A-Za-z]$")) && line2.matches(Regex("^\\$?\\d+\\.\\d{2}$"))) {
        return true
    }
    
    // Merge if both lines are very short (likely fragments)
    if (line1.length <= 5 && line2.length <= 5) {
        return true
    }
}
```

## Expected Results

### Before Fixes:
```
7964 Com
H
H H H H
2.00 $15.75 $2.05 $17.80 $17.80 H
4.00 1.50 4.00 4.25 arama.
DOLLARAMA arama.co
```

### After Fixes:
```
DOLLARAMA
11700 Yonge Street Unit 2
Richmond Hill ON L4E 0K4
HST 863624433
FOAM BOARD 4.00
PINS 1.50
PINS 4.00
BALLOONS 4.25
BALLOON KIT 2.00
SUBTOTAL 15.75
HST 13% 2.05
TOTAL 17.80
AMEX 17.80
```

## Strategy Summary

1. **Primary**: Use ML Kit's built-in line structure (more reliable)
2. **Validation**: Check reconstruction quality before using
3. **Fallback**: Use original text with fragment merging if reconstruction fails
4. **Safety**: Multiple layers of validation and fallback options

This multi-layered approach should handle the Dollarama receipt correctly by either:
- Using ML Kit's line structure if it's working properly
- Falling back to the original text with intelligent fragment merging
- Ensuring the final text preserves key information for field extraction

The app should now correctly identify "DOLLARAMA" as the merchant and "$17.80" as the total amount.
